import warnings
from dataclasses import dataclass
from typing import Any, Literal, Protocol

import numpy as np
from numpy.typing import NDArray

from optimagic import mark
from optimagic.config import IS_PYGAD_INSTALLED
from optimagic.exceptions import NotInstalledError
from optimagic.optimization.algo_options import get_population_size
from optimagic.optimization.algorithm import Algorithm, InternalOptimizeResult
from optimagic.optimization.internal_optimization_problem import (
    InternalOptimizationProblem,
)
from optimagic.typing import (
    AggregationLevel,
    NonNegativeFloat,
    PositiveFloat,
    PositiveInt,
    ProbabilityFloat,
    PyTree,
)


# PyGAD-specific protocol definitions
class ParentSelectionFunction(Protocol):
    """Protocol for user-defined parent selection functions.

    Args:
        fitness: Array of fitness values for all solutions in the population.
        num_parents: Number of parents to select.
        ga_instance: The PyGAD GA instance.

    Returns:
        Tuple of (selected_parents, parent_indices) where:
        - selected_parents: 2D array of selected parent solutions
        - parent_indices: 1D array of indices of selected parents

    """

    def __call__(
        self, fitness: NDArray[np.float64], num_parents: int, ga_instance: Any
    ) -> tuple[NDArray[np.float64], NDArray[np.int_]]: ...


class CrossoverFunction(Protocol):
    """Protocol for user-defined crossover functions.

    Args:
        parents: 2D array of parent solutions selected for mating.
        offspring_size: Tuple (num_offspring, num_genes) specifying
            the shape of the offspring population to be generated.
        ga_instance: The PyGAD GA instance.

    Returns:
        2D array of offspring solutions generated from the parents.

    """

    def __call__(
        self,
        parents: NDArray[np.float64],
        offspring_size: tuple[int, int],
        ga_instance: Any,
    ) -> NDArray[np.float64]: ...


class MutationFunction(Protocol):
    """Protocol for user-defined mutation functions.

    Args:
        offspring: 2D array of offspring solutions to be mutated.
        ga_instance: The PyGAD GA instance.

    Returns:
        2D array of mutated offspring solutions.

    """

    def __call__(
        self, offspring: NDArray[np.float64], ga_instance: Any
    ) -> NDArray[np.float64]: ...


class GeneConstraintFunction(Protocol):
    """Protocol for user-defined gene constraint functions."""

    def __call__(
        self, solution: NDArray[np.float64], values: list[float] | NDArray[np.float64]
    ) -> list[float] | NDArray[np.float64]: ...




@mark.minimizer(
    name="pygad",
    solver_type=AggregationLevel.SCALAR,
    is_available=IS_PYGAD_INSTALLED,
    is_global=True,
    needs_jac=False,
    needs_hess=False,
    needs_bounds=True,
    supports_parallelism=True,
    supports_bounds=True,
    supports_infinite_bounds=False,
    supports_linear_constraints=False,
    supports_nonlinear_constraints=False,
    disable_history=False,
)
@dataclass(frozen=True)
class Pygad(Algorithm):
    """Minimize a scalar function using the PyGAD genetic algorithm.

    PyGAD is a Python library for building genetic algorithms and training machine learning
    algorithms. Genetic algorithms are metaheuristics inspired by the process of natural
    selection that belong to the larger class of evolutionary algorithms. These algorithms
    apply biologically inspired operators such as mutation, crossover, and selection to
    optimization problems.

    The algorithm maintains a population of candidate solutions and iteratively improves
    them through genetic operations, making it ideal for global optimization problems with
    complex search spaces that may contain multiple local optima.

    PyGAD requires finite bounds for all parameters and is particularly well-suited for:

    - Global optimization problems with multiple local optima
    - Discrete or mixed-integer optimization problems
    - Problems where gradient information is not available
    - Optimization problems with complex, non-smooth objective functions
    - Multi-modal optimization landscapes

    The algorithm supports parallel evaluation of fitness functions and provides extensive
    customization options for genetic operators including parent selection, crossover,
    and mutation strategies.

    For more details on PyGAD, see the `PyGAD documentation
    <https://pygad.readthedocs.io/en/latest/>`_.

    """
    population_size: PositiveInt | None = None
    """Number of solutions in each generation.

    When None, optimagic automatically determines an appropriate population size based on
    the problem dimension with a minimum of 10 individuals.
    """

    num_parents_mating: PositiveInt | None = 10
    """Number of parents selected for mating in each generation.

    These parents are used to produce offspring through crossover operations. When None,
    defaults to max(2, population_size // 2).
    """

    num_generations: PositiveInt | None = 50
    """Number of generations to evolve the population.

    Each generation involves selection, crossover, mutation, and replacement operations.
    """

    initial_population: list[PyTree] | None = None
    """Initial population as a list of parameter structures.

    Each element should have the same structure as the starting parameters. When None,
    the population is randomly generated within the parameter bounds using the specified
    population_size and the dimensionality from the starting parameters.
    """

    parent_selection_type: (
        Literal["sss", "rws", "sus", "rank", "random", "tournament"]
        | ParentSelectionFunction
    ) = "sss"
    """Method for selecting parents for mating.

    - "sss": Steady-state selection
    - "rws": Roulette wheel selection
    - "sus": Stochastic universal sampling
    - "rank": Rank-based selection
    - "random": Random selection
    - "tournament": Tournament selection (uses K_tournament parameter)

    Alternatively, provide a custom function with signature:
    ``(fitness, num_parents, ga_instance) -> tuple[NDArray, NDArray]``
    """

    keep_parents: int = -1
    """Number of best parents to keep in the next generation.

    Only has effect when keep_elitism is 0. When -1, all parents are discarded.
    """

    keep_elitism: PositiveInt = 1
    """Number of best solutions to preserve across generations.

    If non-zero, keep_parents has no effect. Elitism ensures the best solutions
    are not lost during evolution.
    """

    K_tournament: PositiveInt = 3
    """Tournament size for tournament selection.

    Only used when parent_selection_type is "tournament". Larger values increase
    selection pressure towards fitter individuals.
    """

    crossover_type: (
        Literal["single_point", "two_points", "uniform", "scattered"]
        | CrossoverFunction
        | None
    ) = "single_point"
    """Crossover method for generating offspring.

    - "single_point": Single-point crossover
    - "two_points": Two-point crossover
    - "uniform": Uniform crossover
    - "scattered": Scattered crossover
    - None: Disable crossover

    Alternatively, provide a custom function with signature:
    ``(parents, offspring_size, ga_instance) -> NDArray``
    """

    crossover_probability: NonNegativeFloat | None = None
    """Probability of applying crossover to generate offspring.

    When None, PyGAD uses its default crossover probability. Range: [0, 1].
    """

    mutation_type: (
        Literal["random", "swap", "inversion", "scramble", "adaptive"]
        | MutationFunction
        | None
    ) = "random"
    """Mutation method for introducing genetic diversity.

    - "random": Random mutation within gene bounds
    - "swap": Swap two randomly selected genes
    - "inversion": Reverse the order of genes in a random segment
    - "scramble": Randomly shuffle genes in a random segment
    - "adaptive": Adaptive mutation that adjusts based on fitness
    - None: Disable mutation

    Alternatively, provide a custom function with signature:
    ``(offspring, ga_instance) -> NDArray``
    """
    mutation_probability: (
        ProbabilityFloat
        | list[ProbabilityFloat]
        | tuple[ProbabilityFloat, ProbabilityFloat]
        | NDArray[np.float64]
        | None
    ) = None
    """Probability of mutation for each gene or generation.

    - Scalar: Single probability applied to all genes/generations
    - List: Probability for each gene (length must match number of genes)
    - Tuple: (min_prob, max_prob) for adaptive mutation probability
    - Array: Probability values for each gene (length must match number of genes)
    """

    mutation_percent_genes: (
        PositiveFloat
        | str
        | list[PositiveFloat]
        | tuple[PositiveFloat, PositiveFloat]
        | NDArray[np.float64]
    ) = "default"
    """Percentage of genes to mutate in each solution.

    - Scalar: Fixed percentage of genes to mutate
    - "default": Use PyGAD's default behavior
    - List: Percentage for each generation (length must match num_generations)
    - Tuple: (min_percent, max_percent) for adaptive mutation percentage
    - Array: Percentage values for each generation
    """

    mutation_num_genes: (
        PositiveInt
        | list[PositiveInt]
        | tuple[PositiveInt, PositiveInt]
        | NDArray[np.int_]
        | None
    ) = None
    """Number of genes to mutate in each solution.

    - Scalar: Fixed number of genes to mutate
    - List: Number for each generation (length must match num_generations)
    - Tuple: (min_num, max_num) for adaptive mutation count
    - Array: Number values for each generation
    """
    mutation_by_replacement: bool = False
    """Whether to replace gene values during mutation.

    Only works with mutation_type="random". When True, mutated genes are replaced
    with new random values. When False, genes are modified additively.
    """

    random_mutation_min_val: float | list[float] | NDArray[np.float64] = -1.0
    """Minimum value for random mutation.

    Only used with mutation_type="random". Can be a scalar (same for all genes),
    list (per-gene values), or array (per-gene values).
    """

    random_mutation_max_val: float | list[float] | NDArray[np.float64] = 1.0
    """Maximum value for random mutation.

    Only used with mutation_type="random". Can be a scalar (same for all genes),
    list (per-gene values), or array (per-gene values).
    """

    allow_duplicate_genes: bool = True
    """Whether to allow duplicate gene values within a solution.

    When False, PyGAD ensures all genes in a solution have unique values.
    """

    gene_constraint: list[GeneConstraintFunction | None] | None = None
    """List of constraint functions for gene values.

    Each function takes (solution, values) and returns filtered values meeting
    constraints. Use None for genes without constraints. Functions should have
    signature: ``(solution, values) -> list[float] | NDArray``
    """

    sample_size: PositiveInt = 100
    """Number of values to sample when finding unique values or enforcing constraints.

    Used when allow_duplicate_genes=False or when gene_constraint is specified.
    """

    batch_size: PositiveInt | None = None
    """Number of solutions to evaluate in parallel batches.

    When None and n_cores > 1, automatically set to n_cores for optimal
    parallelization. Larger values can improve parallel efficiency but use more memory.
    """
    stop_criteria: str | list[str] | None = None
    """Stopping criteria for the genetic algorithm.

    Supported criteria (can be combined in a list):
    - "reach_{value}": Stop when fitness reaches the specified value
    - "saturate_{generations}": Stop when fitness doesn't improve for specified generations

    Examples:
    - "reach_0.01": Stop when fitness reaches 0.01
    - "saturate_10": Stop when fitness doesn't improve for 10 generations
    - ["reach_0.01", "saturate_10"]: Stop when either condition is met
    """

    n_cores: PositiveInt = 1
    """Number of cores for parallel fitness evaluation.

    When greater than 1, fitness functions are evaluated in parallel batches.
    The batch_size parameter controls the batch size for parallel evaluation.
    """

    seed: int | None = None
    """Random seed for reproducibility.

    When specified, ensures reproducible results across runs. When None,
    uses PyGAD's default random behavior.
    """

    def _solve_internal_problem(
        self, problem: InternalOptimizationProblem, x0: NDArray[np.float64]
    ) -> InternalOptimizeResult:
        if not IS_PYGAD_INSTALLED:
            raise NotInstalledError(
                "The 'pygad' algorithm requires the pygad package to be "
                "installed. You can install it with 'pip install pygad'."
            )

        import pygad

        if (
            problem.bounds.lower is None
            or problem.bounds.upper is None
            or not np.isfinite(problem.bounds.lower).all()
            or not np.isfinite(problem.bounds.upper).all()
        ):
            raise ValueError("pygad requires finite bounds for all parameters.")

        # Determine effective batch_size for parallel processing
        effective_batch_size = determine_effective_batch_size(
            self.batch_size, self.n_cores
        )

        if (
            effective_batch_size is not None
            and effective_batch_size > 1
            and self.n_cores > 1
        ):

            def _fitness_func_batch(
                _ga_instance: Any,
                batch_solutions: NDArray[np.float64],
                _batch_indices: list[int] | NDArray[np.int_],
            ) -> list[float]:
                solutions_list: list[NDArray[np.float64]] = [
                    np.asarray(batch_solutions[i])
                    for i in range(batch_solutions.shape[0])
                ]
                batch_results = problem.batch_fun(
                    solutions_list, n_cores=self.n_cores, batch_size=effective_batch_size
                )

                return [-float(result) for result in batch_results]

            fitness_function: Any = _fitness_func_batch
        else:

            def _fitness_func_single(
                _ga_instance: Any, solution: NDArray[np.float64], _solution_idx: int
            ) -> float:
                return -float(problem.fun(solution))

            fitness_function = _fitness_func_single

        population_size = get_population_size(
            population_size=self.population_size, x=x0, lower_bound=10
        )

        num_parents_mating = (
            self.num_parents_mating
            if self.num_parents_mating is not None
            else max(2, population_size // 2)
        )

        if self.initial_population is not None:
            # Convert PyTree list to numpy array using the converter
            initial_population = np.array([
                problem.converter.params_to_internal(params)
                for params in self.initial_population
            ])
        else:
            num_genes = len(x0)

            initial_population = np.random.uniform(
                problem.bounds.lower,
                problem.bounds.upper,
                size=(population_size, num_genes),
            )

            initial_population[0] = x0

        gene_space = [
            {"low": problem.bounds.lower[i], "high": problem.bounds.upper[i]}
            for i in range(len(x0))
        ]

        ga_instance = pygad.GA(
            num_generations=self.num_generations,
            num_parents_mating=num_parents_mating,
            fitness_func=fitness_function,
            fitness_batch_size=effective_batch_size,
            initial_population=initial_population,
            gene_space=gene_space,
            parent_selection_type=self.parent_selection_type,
            keep_parents=self.keep_parents,
            keep_elitism=self.keep_elitism,
            K_tournament=self.K_tournament,
            crossover_type=self.crossover_type,
            crossover_probability=self.crossover_probability,
            mutation_type=self.mutation_type,
            mutation_probability=self.mutation_probability,
            mutation_by_replacement=self.mutation_by_replacement,
            mutation_percent_genes=self.mutation_percent_genes,
            mutation_num_genes=self.mutation_num_genes,
            random_mutation_min_val=self.random_mutation_min_val,
            random_mutation_max_val=self.random_mutation_max_val,
            allow_duplicate_genes=self.allow_duplicate_genes,
            gene_constraint=self.gene_constraint,
            sample_size=self.sample_size,
            stop_criteria=self.stop_criteria,
            parallel_processing=None,
            random_seed=self.seed,
        )

        ga_instance.run()

        result = _process_pygad_result(ga_instance)

        return result


def determine_effective_batch_size(
    batch_size: int | None, n_cores: int
) -> int | None:
    """Determine the effective batch_size for parallel processing.

    Behavior:
    - If `batch_size` is explicitly provided:
        - The value is returned unchanged.
        - A warning is issued if it is less than `n_cores`, as this may
        underutilize available cores.
    - If `batch_size` is `None`:
        - If `n_cores` > 1, defaults to `n_cores`.
        - Otherwise, returns None (i.e., single-threaded evaluation).
    Args:
        batch_size: User-specified batch size or None
        n_cores: Number of cores for parallel processing

    Returns:
        Effective batch size for PyGAD, or None for single-threaded processing

    """
    if batch_size is not None:
        if batch_size < n_cores:
            warnings.warn(
                f"batch_size ({batch_size}) is smaller than "
                f"n_cores ({n_cores}). This may reduce parallel efficiency. "
                f"Consider setting batch_size >= n_cores."
            )
        return batch_size
    elif n_cores > 1:
        return n_cores
    else:
        return None


def _process_pygad_result(ga_instance: Any) -> InternalOptimizeResult:
    """Process PyGAD result into InternalOptimizeResult.

    Args:
        ga_instance: The PyGAD instance after running the optimization

    Returns:
        InternalOptimizeResult: Processed optimization results

    """
    best_solution, best_fitness, _ = ga_instance.best_solution()

    best_criterion = -best_fitness

    completed_generations = ga_instance.generations_completed
    success = ga_instance.run_completed
    if success:
        message = (
            "Optimization terminated successfully.\n"
            f"Generations completed: {completed_generations}"
        )
    else:
        message = (
            "Optimization failed to complete.\n"
            f"Generations completed: {completed_generations}"
        )

    return InternalOptimizeResult(
        x=best_solution,
        fun=best_criterion,
        success=success,
        message=message,
        n_fun_evals=ga_instance.generations_completed * ga_instance.pop_size[0],
    )
