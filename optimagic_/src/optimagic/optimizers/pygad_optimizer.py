import warnings
from dataclasses import dataclass
from typing import Any, Literal, Protocol

import numpy as np
from numpy.typing import NDArray

from optimagic import mark
from optimagic.config import IS_PYGAD_INSTALLED
from optimagic.exceptions import NotInstalledError
from optimagic.optimization.algo_options import get_population_size
from optimagic.optimization.algorithm import Algorithm, InternalOptimizeResult
from optimagic.optimization.internal_optimization_problem import (
    InternalOptimizationProblem,
)
from optimagic.typing import (
    AggregationLevel,
    NonNegativeFloat,
    PositiveFloat,
    PositiveInt,
    ProbabilityFloat,
    PyTree,
)


# PyGAD-specific protocol definitions
class ParentSelectionFunction(Protocol):
    """Protocol for user-defined parent selection functions.

    Args:
        fitness: Array of fitness values for all solutions in the population.
        num_parents: Number of parents to select.
        ga_instance: The PyGAD GA instance.

    Returns:
        Tuple of (selected_parents, parent_indices) where:
        - selected_parents: 2D array of selected parent solutions
        - parent_indices: 1D array of indices of selected parents

    """

    def __call__(
        self, fitness: NDArray[np.float64], num_parents: int, ga_instance: Any
    ) -> tuple[NDArray[np.float64], NDArray[np.int_]]: ...


class CrossoverFunction(Protocol):
    """Protocol for user-defined crossover functions.

    Args:
        parents: 2D array of parent solutions selected for mating.
        offspring_size: Tuple (num_offspring, num_genes) specifying
            the shape of the offspring population to be generated.
        ga_instance: The PyGAD GA instance.

    Returns:
        2D array of offspring solutions generated from the parents.

    """

    def __call__(
        self,
        parents: NDArray[np.float64],
        offspring_size: tuple[int, int],
        ga_instance: Any,
    ) -> NDArray[np.float64]: ...


class MutationFunction(Protocol):
    """Protocol for user-defined mutation functions.

    Args:
        offspring: 2D array of offspring solutions to be mutated.
        ga_instance: The PyGAD GA instance.

    Returns:
        2D array of mutated offspring solutions.

    """

    def __call__(
        self, offspring: NDArray[np.float64], ga_instance: Any
    ) -> NDArray[np.float64]: ...


class GeneConstraintFunction(Protocol):
    """Protocol for user-defined gene constraint functions."""

    def __call__(
        self, solution: NDArray[np.float64], values: list[float] | NDArray[np.float64]
    ) -> list[float] | NDArray[np.float64]: ...




@mark.minimizer(
    name="pygad",
    solver_type=AggregationLevel.SCALAR,
    is_available=IS_PYGAD_INSTALLED,
    is_global=True,
    needs_jac=False,
    needs_hess=False,
    needs_bounds=True,
    supports_parallelism=True,
    supports_bounds=True,
    supports_infinite_bounds=False,
    supports_linear_constraints=False,
    supports_nonlinear_constraints=False,
    disable_history=False,
)
@dataclass(frozen=True)
class Pygad(Algorithm):
    """Minimize a scalar function using the PyGAD genetic algorithm.

    The optimizer uses PyGAD, a comprehensive Python library for building genetic algorithms
    and training machine learning algorithms. PyGAD implements a canonical genetic algorithm
    with extensive customization options for genetic operators.

    Genetic algorithms are population-based metaheuristics inspired by the process of natural
    selection. They maintain a population of candidate solutions and iteratively improve them
    through biologically-inspired operators: selection, crossover (recombination), and mutation.
    The algorithm follows the standard genetic algorithm cycle:

    1. **Initialization**: Create an initial population of candidate solutions
    2. **Evaluation**: Compute fitness values for all individuals
    3. **Selection**: Choose parents for reproduction based on fitness
    4. **Crossover**: Generate offspring by combining parent solutions
    5. **Mutation**: Introduce random variations to maintain diversity
    6. **Replacement**: Form the next generation from parents and offspring
    7. **Termination**: Repeat until stopping criteria are met

    The PyGAD implementation is particularly well-suited for:

    - **Global optimization problems** with multiple local optima and complex landscapes
    - **Black-box optimization** where gradient information is unavailable or unreliable
    - **Discrete and mixed-integer optimization** problems
    - **Multi-modal optimization** with multiple acceptable solutions
    - **Constrained optimization** with complex feasibility regions
    - **Noisy objective functions** where exact evaluations are difficult

    **Performance Characteristics:**

    - **Computational complexity**: O(P × G × F) where P is population size, G is generations,
      and F is the cost of fitness evaluation
    - **Memory usage**: O(P × D) where D is problem dimension
    - **Convergence**: No theoretical guarantees, but empirically robust for global optimization
    - **Parallelization**: Supports parallel fitness evaluation with configurable batch sizes

    **Algorithm Properties:**

    - **Scale sensitivity**: Moderately sensitive to parameter scaling; bounds normalization
      can improve performance
    - **Parameter tuning**: Requires careful tuning of population size, selection pressure,
      and genetic operator parameters
    - **Premature convergence**: Risk of losing diversity; controlled by mutation rates and
      selection strategies

    PyGAD requires finite bounds for all parameters and supports extensive customization of
    genetic operators including parent selection strategies, crossover methods, and mutation
    techniques. The implementation includes advanced features like elitism, adaptive operators,
    and parallel fitness evaluation.

    For detailed algorithmic descriptions and theoretical background, see Gad (2023) and the
    `PyGAD documentation <https://pygad.readthedocs.io/en/latest/>`_.

    References:
        Gad, A. F. (2023). PyGAD: An intuitive genetic algorithm Python library.
        Multimedia Tools and Applications, 1-14.

    """
    population_size: PositiveInt | None = None
    r"""Number of solutions in each generation.

    The population size P directly affects the algorithm's exploration capability and
    computational cost. Larger populations provide better exploration of the search space
    but require more fitness evaluations per generation.

    **Recommended values:**
    - Small problems (D ≤ 10): P = 20-50
    - Medium problems (10 < D ≤ 100): P = 50-200
    - Large problems (D > 100): P = 100-500

    **Performance implications:**
    - Larger P increases diversity and global search capability
    - Smaller P reduces computational cost but may cause premature convergence
    - Rule of thumb: P ≥ 2D for adequate exploration

    When None, optimagic automatically sets P = max(10, 2 × D) where D is the problem
    dimension.
    """

    num_parents_mating: PositiveInt | None = 10
    r"""Number of parents selected for mating in each generation.

    This parameter controls the selection pressure in the algorithm. The selection pressure
    s is defined as s = num_parents_mating / population_size.

    **Selection pressure effects:**
    - High pressure (s > 0.7): Fast convergence, risk of premature convergence
    - Medium pressure (0.3 ≤ s ≤ 0.7): Balanced exploration and exploitation
    - Low pressure (s < 0.3): Slow convergence, better diversity preservation

    **Recommended values:**
    - s ≈ 0.5 for most problems (balanced approach)
    - s ≈ 0.3 for highly multi-modal problems
    - s ≈ 0.7 for unimodal or well-behaved problems

    When None, defaults to max(2, population_size // 2), providing moderate selection
    pressure.
    """

    num_generations: PositiveInt | None = 50
    """Number of generations to evolve the population.

    Each generation involves the complete genetic algorithm cycle: evaluation, selection,
    crossover, mutation, and replacement. The total number of fitness evaluations is
    approximately population_size × num_generations.

    **Convergence considerations:**
    - Most improvement typically occurs in the first 50-100 generations
    - Diminishing returns after initial rapid improvement phase
    - Monitor convergence to avoid unnecessary computation

    **Recommended values:**
    - Quick optimization: 50-100 generations
    - Thorough search: 200-500 generations
    - Research/benchmarking: 1000+ generations
    """

    initial_population: list[PyTree] | None = None
    """Initial population as a list of parameter structures.

    The initial population can significantly impact convergence speed and final solution
    quality. Each element should have the same PyTree structure as the starting parameters.

    **Population initialization strategies:**
    - **Random uniform**: Default when None; samples uniformly within bounds
    - **Custom seeding**: Provide specific starting points based on domain knowledge
    - **Hybrid initialization**: Mix random and heuristic solutions

    **Performance implications:**
    - Good initial population can reduce convergence time by 20-50%
    - Diverse initialization prevents premature convergence
    - Including known good solutions can improve final quality

    **Best practices:**
    - Ensure population diversity to avoid clustering
    - Include the provided starting parameters as one individual
    - For constrained problems, ensure all individuals are feasible

    When None, the population is randomly generated using uniform sampling within the
    parameter bounds, with the first individual set to the starting parameters.
    """

    parent_selection_type: (
        Literal["sss", "rws", "sus", "rank", "random", "tournament"]
        | ParentSelectionFunction
    ) = "sss"
    r"""Method for selecting parents for mating.

    The selection method determines how parents are chosen for reproduction, directly
    affecting selection pressure and convergence behavior.

    **Selection methods:**

    - **"sss"** (Steady-State Selection): Maintains population diversity by selecting
      parents with moderate bias toward fitness
    - **"rws"** (Roulette Wheel Selection): Probability of selection proportional to
      fitness. Selection probability: p_i = f_i / Σf_j where f_i is individual fitness
    - **"sus"** (Stochastic Universal Sampling): Low-variance version of roulette wheel
      with evenly spaced selection pointers
    - **"rank"** (Rank-Based Selection): Selection based on fitness rank rather than
      raw fitness values, reducing selection pressure
    - **"random"**: Uniform random selection, no selection pressure
    - **"tournament"**: k-tournament selection with tournament size K_tournament

    **Selection pressure comparison:**
    - Highest: tournament (k > 5) > roulette wheel > rank-based > random
    - Lowest: random selection

    **Performance implications:**
    - High pressure: Faster convergence, higher risk of premature convergence
    - Low pressure: Better diversity, slower convergence
    - SUS provides more consistent selection than roulette wheel

    **Recommended choices:**
    - "sss": General-purpose, balanced approach (default)
    - "tournament": When fine control over selection pressure is needed
    - "rank": For problems with highly variable fitness landscapes
    - "sus": When consistent selection behavior is important

    Alternatively, provide a custom function with signature:
    ``(fitness, num_parents, ga_instance) -> tuple[NDArray, NDArray]``
    """

    keep_parents: int = -1
    """Number of best parents to keep in the next generation.

    This parameter implements a form of elitism when keep_elitism is 0. Parent
    preservation can help maintain good solutions but may reduce population diversity.

    **Values:**
    - -1: Discard all parents (generational replacement)
    - 0: No parent preservation
    - k > 0: Keep k best parents

    **Performance implications:**
    - Higher values preserve good solutions but may slow exploration
    - Generational replacement (-1) provides maximum diversity
    - Only effective when keep_elitism = 0
    """

    keep_elitism: PositiveInt = 1
    r"""Number of best solutions to preserve across generations.

    Elitism ensures that the best solutions are never lost during evolution, providing
    a monotonic improvement guarantee for the best fitness value.

    **Mathematical guarantee:**
    If E > 0, then best_fitness(generation_t+1) ≥ best_fitness(generation_t)

    **Recommended values:**
    - E = 1-2: Standard elitism for most problems
    - E = 5-10% of population: High elitism for noisy fitness functions
    - E = 0: No elitism (not recommended for most problems)

    **Performance implications:**
    - Prevents loss of good solutions due to genetic operators
    - Reduces population diversity if too high (E > 10% of population)
    - Essential for problems with noisy or stochastic fitness evaluation

    When non-zero, this parameter takes precedence over keep_parents.
    """

    K_tournament: PositiveInt = 3
    r"""Tournament size for tournament selection.

    Only used when parent_selection_type is "tournament". Tournament selection chooses
    k individuals randomly and selects the best among them.

    **Selection pressure relationship:**
    The probability that the best individual is selected in k-tournament is:
    P(best selected) = 1 - (1 - 1/N)^k where N is population size

    **Recommended values:**
    - k = 2: Low selection pressure, good for maintaining diversity
    - k = 3-5: Moderate selection pressure (recommended for most problems)
    - k = 7-10: High selection pressure, fast convergence
    - k > 10: Very high pressure, risk of premature convergence

    **Performance implications:**
    - Larger k increases selection pressure exponentially
    - k = 2 provides gentle selection pressure
    - k ≥ population_size/2 creates very high selection pressure
    """

    crossover_type: (
        Literal["single_point", "two_points", "uniform", "scattered"]
        | CrossoverFunction
        | None
    ) = "single_point"
    r"""Crossover method for generating offspring.

    Crossover combines genetic material from two parents to create offspring, enabling
    the algorithm to explore new regions of the search space by recombining successful
    solution components.

    **Crossover methods:**

    - **"single_point"**: Choose random point k, offspring₁ = parent₁[0:k] + parent₂[k:D]
    - **"two_points"**: Choose points k₁, k₂, swap middle segment between parents
    - **"uniform"**: Each gene inherited from parent₁ or parent₂ with probability 0.5
    - **"scattered"**: Similar to uniform but with different probability distribution
    - **None**: Disable crossover (offspring = exact copies of parents)

    **Disruption levels:**
    - Lowest: single_point (preserves large building blocks)
    - Medium: two_points (moderate disruption)
    - Highest: uniform/scattered (maximum mixing, disrupts building blocks)

    **Performance implications:**
    - Single-point: Good for problems with positional gene dependencies
    - Two-point: Balanced approach for most problems
    - Uniform: Best for problems with independent gene contributions
    - Scattered: Alternative uniform implementation with different randomization

    **Recommended choices:**
    - "single_point": Default choice, works well for most problems
    - "uniform": When genes are largely independent
    - "two_points": When moderate disruption is desired

    Alternatively, provide a custom function with signature:
    ``(parents, offspring_size, ga_instance) -> NDArray``
    """

    crossover_probability: NonNegativeFloat | None = None
    r"""Probability of applying crossover to generate offspring.

    Controls the frequency of crossover operations. When crossover is not applied,
    offspring are exact copies of selected parents.

    **Typical values:**
    - p_c = 0.6-0.9: Standard range for most problems
    - p_c = 0.8-0.95: High crossover rate for exploration-heavy search
    - p_c = 0.5-0.7: Lower rate when mutation is the primary operator

    **Performance implications:**
    - Higher p_c increases exploration through recombination
    - Lower p_c relies more on mutation for variation
    - p_c = 1.0 may be too disruptive for fine-tuning
    - p_c = 0.0 disables crossover entirely

    When None, PyGAD uses its default crossover probability (typically 0.8).
    """

    mutation_type: (
        Literal["random", "swap", "inversion", "scramble", "adaptive"]
        | MutationFunction
        | None
    ) = "random"
    r"""Mutation method for introducing genetic diversity.

    Mutation introduces random variations to prevent premature convergence and maintain
    population diversity. Different mutation types are suited for different problem
    characteristics.

    **Mutation methods:**

    - **"random"**: Replace selected genes with random values within bounds.
      For gene i: x'ᵢ = random(lower_bound_i, upper_bound_i)
    - **"swap"**: Exchange values between two randomly selected genes.
      Preserves gene values but changes their positions
    - **"inversion"**: Reverse gene order in a random segment [i, j].
      Maintains gene values while changing local structure
    - **"scramble"**: Randomly shuffle genes within a selected segment.
      Similar to inversion but with random reordering
    - **"adaptive"**: Mutation rate adapts based on population fitness diversity.
      Higher mutation when population converges
    - **None**: Disable mutation (not recommended for most problems)

    **Problem suitability:**
    - **Random**: Best for continuous optimization, respects bounds
    - **Swap**: Good for permutation problems, discrete optimization
    - **Inversion**: Effective for problems with positional dependencies
    - **Scramble**: Alternative to inversion with different disruption pattern
    - **Adaptive**: Self-tuning approach for varying problem phases

    **Performance implications:**
    - Random mutation provides the strongest diversity injection
    - Swap/inversion/scramble preserve existing gene values
    - Adaptive mutation automatically balances exploration/exploitation
    - Disabling mutation often leads to premature convergence

    **Recommended choices:**
    - "random": Default for continuous optimization problems
    - "adaptive": When automatic parameter tuning is desired
    - "swap": For discrete or permutation-based problems

    Alternatively, provide a custom function with signature:
    ``(offspring, ga_instance) -> NDArray``
    """
    mutation_probability: (
        ProbabilityFloat
        | list[ProbabilityFloat]
        | tuple[ProbabilityFloat, ProbabilityFloat]
        | NDArray[np.float64]
        | None
    ) = None
    """Probability of mutation for each gene or generation.

    - Scalar: Single probability applied to all genes/generations
    - List: Probability for each gene (length must match number of genes)
    - Tuple: (min_prob, max_prob) for adaptive mutation probability
    - Array: Probability values for each gene (length must match number of genes)
    """

    mutation_percent_genes: (
        PositiveFloat
        | str
        | list[PositiveFloat]
        | tuple[PositiveFloat, PositiveFloat]
        | NDArray[np.float64]
    ) = "default"
    """Percentage of genes to mutate in each solution.

    - Scalar: Fixed percentage of genes to mutate
    - "default": Use PyGAD's default behavior
    - List: Percentage for each generation (length must match num_generations)
    - Tuple: (min_percent, max_percent) for adaptive mutation percentage
    - Array: Percentage values for each generation
    """

    mutation_num_genes: (
        PositiveInt
        | list[PositiveInt]
        | tuple[PositiveInt, PositiveInt]
        | NDArray[np.int_]
        | None
    ) = None
    """Number of genes to mutate in each solution.

    - Scalar: Fixed number of genes to mutate
    - List: Number for each generation (length must match num_generations)
    - Tuple: (min_num, max_num) for adaptive mutation count
    - Array: Number values for each generation
    """
    mutation_by_replacement: bool = False
    """Whether to replace gene values during mutation.

    Only works with mutation_type="random". When True, mutated genes are replaced
    with new random values. When False, genes are modified additively.
    """

    random_mutation_min_val: float | list[float] | NDArray[np.float64] = -1.0
    """Minimum value for random mutation.

    Only used with mutation_type="random". Can be a scalar (same for all genes),
    list (per-gene values), or array (per-gene values).
    """

    random_mutation_max_val: float | list[float] | NDArray[np.float64] = 1.0
    """Maximum value for random mutation.

    Only used with mutation_type="random". Can be a scalar (same for all genes),
    list (per-gene values), or array (per-gene values).
    """

    allow_duplicate_genes: bool = True
    """Whether to allow duplicate gene values within a solution.

    When False, PyGAD ensures all genes in a solution have unique values.
    """

    gene_constraint: list[GeneConstraintFunction | None] | None = None
    """List of constraint functions for gene values.

    Each function takes (solution, values) and returns filtered values meeting
    constraints. Use None for genes without constraints. Functions should have
    signature: ``(solution, values) -> list[float] | NDArray``
    """

    sample_size: PositiveInt = 100
    """Number of values to sample when finding unique values or enforcing constraints.

    Used when allow_duplicate_genes=False or when gene_constraint is specified.
    """

    batch_size: PositiveInt | None = None
    r"""Number of solutions to evaluate in parallel batches.

    Batch evaluation can significantly reduce wall-clock time for expensive fitness
    functions by leveraging parallel processing capabilities.

    **Performance characteristics:**
    - **Parallel efficiency**: η = T_serial / (n_cores × T_parallel)
    - **Optimal batch size**: Usually equals n_cores for CPU-bound tasks
    - **Memory usage**: O(batch_size × problem_dimension)

    **Recommended values:**
    - batch_size = n_cores: Optimal for most CPU-bound fitness functions
    - batch_size > n_cores: When I/O or memory bandwidth is limiting factor
    - batch_size < n_cores: When memory constraints are significant

    **Performance implications:**
    - Larger batches reduce scheduling overhead but increase memory usage
    - Smaller batches provide better load balancing for variable evaluation times
    - Batch size should not exceed population_size

    **Automatic sizing:**
    When None and n_cores > 1, automatically set to n_cores. When n_cores = 1,
    batch evaluation is disabled for optimal single-threaded performance.

    **Warning:** Setting batch_size < n_cores may underutilize available cores.
    """
    stop_criteria: str | list[str] | None = None
    r"""Stopping criteria for the genetic algorithm.

    Early stopping can significantly reduce computational cost by terminating when
    further improvement is unlikely or when a target fitness is reached.

    **Supported criteria:**

    - **"reach_{value}"**: Stop when best fitness ≥ target value
      Condition: max(fitness) ≥ target_value
    - **"saturate_{generations}"**: Stop when no improvement for k generations
      Condition: best_fitness(t) = best_fitness(t-k) for k consecutive generations

    **Multiple criteria:**
    When multiple criteria are provided in a list, the algorithm stops when ANY
    condition is satisfied (logical OR operation).

    **Performance implications:**
    - "reach_*" prevents unnecessary computation after target achievement
    - "saturate_*" detects convergence and prevents wasted evaluations
    - Saturation detection is essential for problems without known target values
    - Too aggressive saturation (small k) may stop prematurely

    **Recommended values:**
    - Saturation: 10-50 generations depending on problem difficulty
    - Reach: Set based on known optimal values or acceptable solution quality

    **Examples:**
    - "reach_0.01": Stop when fitness reaches 0.01
    - "saturate_20": Stop after 20 generations without improvement
    - ["reach_0.01", "saturate_50"]: Stop when either condition is met

    When None, the algorithm runs for the full num_generations.
    """

    n_cores: PositiveInt = 1
    r"""Number of cores for parallel fitness evaluation.

    Parallel evaluation can provide significant speedup for computationally expensive
    fitness functions. The theoretical speedup is limited by Amdahl's law.

    **Speedup estimation:**
    - **Ideal speedup**: S = n_cores (for perfectly parallelizable fitness functions)
    - **Realistic speedup**: S ≈ n_cores × 0.7-0.9 (accounting for overhead)
    - **Diminishing returns**: Beyond 8-16 cores for typical problems

    **Performance considerations:**
    - Fitness function must be thread-safe for parallel evaluation
    - Overhead becomes significant for very fast fitness functions (< 1ms)
    - Memory usage scales with n_cores × batch_size
    - I/O bound functions may not benefit from high core counts

    **Recommended values:**
    - n_cores = 1: For fast fitness functions or debugging
    - n_cores = 4-8: Good balance for most problems
    - n_cores = available_cores: For expensive fitness functions

    **Best practices:**
    - Monitor CPU utilization to verify effective parallelization
    - Consider memory constraints when setting high core counts
    - Test with different core counts to find optimal configuration
    """

    seed: int | None = None
    """Random seed for reproducibility.

    Setting a seed ensures deterministic behavior across multiple runs, which is
    essential for algorithm comparison, debugging, and scientific reproducibility.

    **Reproducibility scope:**
    - Initial population generation
    - Parent selection randomization
    - Crossover point selection
    - Mutation operations
    - All other stochastic algorithm components

    **Best practices:**
    - Always set seed for research and benchmarking
    - Use different seeds for multiple independent runs
    - Document seed values in experimental reports
    - Consider seed sensitivity when reporting results

    When None, PyGAD uses system time or random device for initialization,
    resulting in non-deterministic behavior.
    """

    def _solve_internal_problem(
        self, problem: InternalOptimizationProblem, x0: NDArray[np.float64]
    ) -> InternalOptimizeResult:
        if not IS_PYGAD_INSTALLED:
            raise NotInstalledError(
                "The 'pygad' algorithm requires the pygad package to be "
                "installed. You can install it with 'pip install pygad'."
            )

        import pygad

        if (
            problem.bounds.lower is None
            or problem.bounds.upper is None
            or not np.isfinite(problem.bounds.lower).all()
            or not np.isfinite(problem.bounds.upper).all()
        ):
            raise ValueError("pygad requires finite bounds for all parameters.")

        # Determine effective batch_size for parallel processing
        effective_batch_size = determine_effective_batch_size(
            self.batch_size, self.n_cores
        )

        if (
            effective_batch_size is not None
            and effective_batch_size > 1
            and self.n_cores > 1
        ):

            def _fitness_func_batch(
                _ga_instance: Any,
                batch_solutions: NDArray[np.float64],
                _batch_indices: list[int] | NDArray[np.int_],
            ) -> list[float]:
                solutions_list: list[NDArray[np.float64]] = [
                    np.asarray(batch_solutions[i])
                    for i in range(batch_solutions.shape[0])
                ]
                batch_results = problem.batch_fun(
                    solutions_list, n_cores=self.n_cores, batch_size=effective_batch_size
                )

                return [-float(result) for result in batch_results]

            fitness_function: Any = _fitness_func_batch
        else:

            def _fitness_func_single(
                _ga_instance: Any, solution: NDArray[np.float64], _solution_idx: int
            ) -> float:
                return -float(problem.fun(solution))

            fitness_function = _fitness_func_single

        population_size = get_population_size(
            population_size=self.population_size, x=x0, lower_bound=10
        )

        num_parents_mating = (
            self.num_parents_mating
            if self.num_parents_mating is not None
            else max(2, population_size // 2)
        )

        if self.initial_population is not None:
            # Convert PyTree list to numpy array using the converter
            initial_population = np.array([
                problem.converter.params_to_internal(params)
                for params in self.initial_population
            ])
        else:
            num_genes = len(x0)

            initial_population = np.random.uniform(
                problem.bounds.lower,
                problem.bounds.upper,
                size=(population_size, num_genes),
            )

            initial_population[0] = x0

        gene_space = [
            {"low": problem.bounds.lower[i], "high": problem.bounds.upper[i]}
            for i in range(len(x0))
        ]

        ga_instance = pygad.GA(
            num_generations=self.num_generations,
            num_parents_mating=num_parents_mating,
            fitness_func=fitness_function,
            fitness_batch_size=effective_batch_size,
            initial_population=initial_population,
            gene_space=gene_space,
            parent_selection_type=self.parent_selection_type,
            keep_parents=self.keep_parents,
            keep_elitism=self.keep_elitism,
            K_tournament=self.K_tournament,
            crossover_type=self.crossover_type,
            crossover_probability=self.crossover_probability,
            mutation_type=self.mutation_type,
            mutation_probability=self.mutation_probability,
            mutation_by_replacement=self.mutation_by_replacement,
            mutation_percent_genes=self.mutation_percent_genes,
            mutation_num_genes=self.mutation_num_genes,
            random_mutation_min_val=self.random_mutation_min_val,
            random_mutation_max_val=self.random_mutation_max_val,
            allow_duplicate_genes=self.allow_duplicate_genes,
            gene_constraint=self.gene_constraint,
            sample_size=self.sample_size,
            stop_criteria=self.stop_criteria,
            parallel_processing=None,
            random_seed=self.seed,
        )

        ga_instance.run()

        result = _process_pygad_result(ga_instance)

        return result


def determine_effective_batch_size(
    batch_size: int | None, n_cores: int
) -> int | None:
    """Determine the effective batch_size for parallel processing.

    Behavior:
    - If `batch_size` is explicitly provided:
        - The value is returned unchanged.
        - A warning is issued if it is less than `n_cores`, as this may
        underutilize available cores.
    - If `batch_size` is `None`:
        - If `n_cores` > 1, defaults to `n_cores`.
        - Otherwise, returns None (i.e., single-threaded evaluation).
    Args:
        batch_size: User-specified batch size or None
        n_cores: Number of cores for parallel processing

    Returns:
        Effective batch size for PyGAD, or None for single-threaded processing

    """
    if batch_size is not None:
        if batch_size < n_cores:
            warnings.warn(
                f"batch_size ({batch_size}) is smaller than "
                f"n_cores ({n_cores}). This may reduce parallel efficiency. "
                f"Consider setting batch_size >= n_cores."
            )
        return batch_size
    elif n_cores > 1:
        return n_cores
    else:
        return None


def _process_pygad_result(ga_instance: Any) -> InternalOptimizeResult:
    """Process PyGAD result into InternalOptimizeResult.

    Args:
        ga_instance: The PyGAD instance after running the optimization

    Returns:
        InternalOptimizeResult: Processed optimization results

    """
    best_solution, best_fitness, _ = ga_instance.best_solution()

    best_criterion = -best_fitness

    completed_generations = ga_instance.generations_completed
    success = ga_instance.run_completed
    if success:
        message = (
            "Optimization terminated successfully.\n"
            f"Generations completed: {completed_generations}"
        )
    else:
        message = (
            "Optimization failed to complete.\n"
            f"Generations completed: {completed_generations}"
        )

    return InternalOptimizeResult(
        x=best_solution,
        fun=best_criterion,
        success=success,
        message=message,
        n_fun_evals=ga_instance.generations_completed * ga_instance.pop_size[0],
    )
